# Import Profiles Feature

## Purpose
Automated system for importing data from external systems (APIs or FTP) into reference tables, with scheduling and data transformation.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/admin/import-profiles/index.vue` – Import profiles list
- `/pages/admin/import-profiles/[id].vue` – Profile configuration with wizard (stepper)

### Components
#### Step Wrapper
- `/components/import-profiles/ImportProfileStepWrapper.vue` – Wizard step wrapper
- `/components/import-profiles/ImportProfilesList.vue` – List of configured profiles

#### Forms
- `/components/import-profiles/forms/ImportProfileConfigurationForm.vue` – Basic configuration
- `/components/import-profiles/forms/ImportProfileIntegrationForm.vue` – Integration wrapper
- `/components/import-profiles/forms/ImportProfileIntegrationApiForm.vue` – API configuration
- `/components/import-profiles/forms/ImportProfileIntegrationFtpForm.vue` – FTP configuration
- `/components/import-profiles/forms/ImportProfileLoadMapping.vue` – Mapping upload
- `/components/import-profiles/forms/ImportProfileMappingForm.vue` – Field mapping
- `/components/import-profiles/forms/ImportProfileScheduleForm.vue` – Scheduling configuration

### API Services
- `/api/import-profiles/` – CRUD and profile operations

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API operations
- Uses `dayjs-nuxt` for scheduling configuration
- Uses `@vueuse/core` for utilities
