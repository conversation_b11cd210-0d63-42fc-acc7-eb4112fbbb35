# API Client Feature

## Purpose
System for creating API keys that allow other systems to access specific data (read or modify) in Hyperfox through token-based authentication.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/admin/api-clients/index.vue` – API clients list
- `/pages/admin/api-clients/[id].vue` – API client configuration with wizard (stepper)

### Components
#### Main Components
- `/components/api-clients/ApiClientStepWrapper.vue` – Wizard step wrapper
- `/components/api-clients/ApiClientCredentials.vue` – Credential display

#### Forms
- `/components/api-clients/forms/ApiClientScopeForm.vue` – Permissions form

### API Services
- `/api/api-clients/` – API client CRUD and operations

### Types & Schemas
- `/utils/types/api-client/` – Data types for forms and credentials

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API mutations and queries
- Uses `@vueuse/core` for clipboard functionality
