# Global Plugins & Key Directories Overview

## Global Plugins

The following plugins are configured in `nuxt.config.ts`:

### Plugins in `/app/plugins/`:
- `base64.ts` - Base64 encoding/decoding utilities
- `api-base-url.ts` - Base API URL configuration
- `vue-query.ts` - TanStack Query setup for data fetching
- `clarity.ts` - Microsoft Clarity analytics integration

## Key Directories

### Main directory structure in `/app/`:

- `/components/` - Reusable UI components
- `/composables/` - Vue composable functions
- `/stores/` - State management with Pinia
- `/pages/` - Route-based views
- `/api/` - API clients and services
- `/utils/` - Utility functions and types
- `/middleware/` - Route middleware
- `/layouts/` - Page layouts
- `/assets/` - Static assets
