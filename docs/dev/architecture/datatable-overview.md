# DataTable Feature

## Purpose
Dynamic table system for displaying and editing data with support for multiple cell types and automatic configuration.

---------------------------------------------------------------|
## File Structure

### Core Components
- `/components/tables/DataTable.vue` – Main dynamic table component
- `/components/tables/DefaultHeader.vue` – Standard table headers
- `/components/tables/MappingHeader.vue` – Headers for field mapping (import profiles)
- `/components/tables/TableSkeleton.vue` – Skeleton loader for tables

### Table Cells (View Mode)
- `/components/tables/table-cells/TextCell.vue` – Plain text
- `/components/tables/table-cells/NumericCell.vue` – Numbers
- `/components/tables/table-cells/DateCell.vue` – Dates
- `/components/tables/table-cells/DateTimeCell.vue` – Date with time
- `/components/tables/table-cells/BadgeCell.vue` – Colored labels
- `/components/tables/table-cells/RelationshipCell.vue` – Related table data
- `/components/tables/table-cells/ActionsCell.vue` – Action buttons
- `/components/tables/table-cells/ErrorCell.vue` – Error indicators

### Table Cells (Edit Mode)
- `/components/tables/table-cells/EditableTextCell.vue` – Text input
- `/components/tables/table-cells/EditableNumericCell.vue` – Numeric input
- `/components/tables/table-cells/EditableDateCell.vue` – Date picker
- `/components/tables/table-cells/EditableDateTimeCell.vue` – Date & time picker
- `/components/tables/table-cells/EditableRelationshipCell.vue` – Dropdown for relationships

### Composables & Utils
- `/composables/table/useColumnComponentMap.ts` – Maps column types to components
- `/utils/table/` – Utilities for column generation and configuration

---------------------------------------------------------------|
## Global Dependencies
- Uses `@tanstack/vue-table` for table functionality
- Uses `@vueuse/core` for utilities like clipboard
- Uses `vue-query.ts` for data fetching in relationship cells
