# Webhooks Feature

## Purpose
Automated notification system that sends HTTP alerts to configured URLs when orders are validated.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/admin/webhooks/index.vue` – Webhooks list
- `/pages/admin/webhooks/[id].vue` – Individual webhook configuration

### Components
#### Main Components
- `/components/webhooks/WebhooksList.vue` – List of configured webhooks

#### Forms
- `/components/webhooks/forms/WebhookConfigurationForm.vue` – Webhook configuration form

### API Services
- `/api/webhooks/` – CRUD and webhook operations

### Types & Schemas
- `/utils/types/webhook/webhook-form.type.ts` – Form types
- `/schemas/webhooks/` – Form validations

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API operations
- Uses `@vueuse/core` for clipboard functionality
