# Order Management Feature

## Purpose
Control center for processing and validating orders received via email or external channels (e.g., MYNUMA). Manages the full flow from receiving the order to validation, including AI processing and archiving of approved/rejected orders.

---------------------------------------------------------------|
## File Structure

### Pages
#### Processing Flow
- `/pages/to-process/index.vue` – Orders pending processing
- `/pages/validate/index.vue` – Orders pending validation
- `/pages/validate/[id].vue` – Individual order validation
- `/pages/archive/index.vue` – Archived orders
- `/pages/archive/[id].vue` – Archived order view

#### Order Inquiries
- `/pages/order-inquiries/index.vue` – Inquiries list
- `/pages/order-inquiries/[id].vue` – Inquiry detail

### Components
#### Order Display
- `/components/orders/OrderInquiry.vue` – Inquiry display (email + attachments)
- `/components/orders/Attachment.vue` – Attachment viewer

#### Error Handling
- `/components/errors/OrderErrorReasonAlert.vue` – Order error alerts
- `/components/errors/CollapsibleErrorAlert.vue` – Collapsible errors with details

### API Services
- `/api/assets/` – CRUD for dynamic orders
- `/api/order-inquiries/` – Inquiries management

### Stores
- `/stores/order-inquiry.store.ts` – Inquiries state
- `/stores/asset.store.ts` – Dynamic orders state

### Utils
- `/utils/validate/` – Validation actions (save, approve, reject)

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API operations
- Uses `dayjs-nuxt` for date formatting
- Uses `@vueuse/core` for utilities like clipboard
