# Integrations Feature

## Purpose
Manages connections to external systems (FTP/SFTP), which are used by Import and Export Profiles for data transfer.

## File Structure

### Pages
- `/pages/admin/integrations/index.vue` – Integrations list
- `/pages/admin/integrations/[id].vue` – Individual integration configuration

### Components
#### Main Components
- `/components/integrations/IntegrationsContainer.vue` – Main container (legacy)
- `/components/integrations/IntegrationsFormContainer.vue` – Form wrapper
- `/components/integrations/IntegrationsList.vue` – List of active integrations
- `/components/integrations/IntegrationsAvailable.vue` – Available integrations

#### Forms
- `/components/integrations/forms/IntegrationsUpsertForm.vue` – Main form wrapper
- `/components/integrations/forms/IntegrationsUpsertApi.vue` – API configuration
- `/components/integrations/forms/IntegrationsUpsertFtp.vue` – FTP configuration

### API Services
- `/api/integrations/` – Integrations CRUD

### Stores
- `/stores/integrations.store.ts` – Integrations state

## Global Dependencies
- Uses `vue-query.ts` for API operations
- Uses `@vueuse/core` for utilities
