# Master Data Feature

## Purpose
System for defining and configuring the structure of dynamic data managed by the application. It allows creating data types (Asset Types), defining their fields, relationships, forms, and configuring how they are displayed in tables—adapting to different business types.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/admin/master-data/index.vue` – Main list of Asset Types
- `/pages/admin/master-data/[id].vue` – Detailed Asset Type configuration with tabs

### Components
#### Forms
- `/components/forms/assetType/AddAssetTypeForm.vue` – Create new Asset Type
- `/components/forms/assetType/EditAssetTypeForm.vue` – Edit Asset Type
- `/components/forms/assetType/AddAssetTypeFieldForm.vue` – Add fields
- `/components/forms/assetType/EditAssetTypeFieldForm.vue` – Edit fields
- `/components/forms/assetType/AddAssetTypeRelationshipForm.vue` – Create relationships
- `/components/forms/assetType/EditAssetTypeRelationshipForm.vue` – Edit relationships
- `/components/forms/assetType/AddAssetTypeIndexForm.vue` – Create indexes
- `/components/forms/assetType/UpsertAssetTypeFormFieldForm.vue` – Configure form fields
- `/components/forms/assetType/UpsertAssetTypeTableFieldForm.vue` – Configure table fields

#### Tables
- `/components/tables/assetType/AssetTypeFieldsTable.vue` – Fields table
- `/components/tables/assetType/AssetTypeRelationshipsTable.vue` – Relationships table
- `/components/tables/assetType/AssetTypeIndexesTable.vue` – Indexes table
- `/components/tables/assetType/AssetTypeFormFieldsTable.vue` – Form fields table
- `/components/tables/assetType/AssetTypeTableFieldsTable.vue` – Table fields table

### API Services
- `/api/asset-types/` – Asset Types CRUD
- `/api/asset-type-fields/` – Field management
- `/api/asset-type-form-fields/` – Form configuration
- `/api/asset-type-table-fields/` – Table configuration

### Stores
- `/stores/asset-type.store.ts` – Asset Types state
- `/stores/asset-type-field.store.ts` – Fields state
- `/stores/asset-type-relationship.store.ts` – Relationships state
- `/stores/asset-type-index.store.ts` – Indexes state
- `/stores/asset-type-form-field.store.ts` – Form fields state
- `/stores/asset-type-table-field.store.ts` – Table fields state

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API operations
- Uses `@vueuse/core` for utilities
- Uses `@tanstack/vue-table` for advanced table features
