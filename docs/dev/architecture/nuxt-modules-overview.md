# Nuxt Modules Overview

## Active Modules

The following modules are configured in `nuxt.config.ts`:

### Project Modules:
- `@pinia/nuxt` – Application state management
- `@nuxt/ui-pro` – Premium UI components
- `@nuxtjs/i18n` – Internationalization (EN/NL)
- `nuxt-lodash` – Integrates Lodash functions with auto-import (useCloneDeep, useDebounce, etc.)
- `@nuxt/image` – Image optimization
- `@nuxt/eslint` – Code linting
- `dayjs-nuxt` – Date manipulation
- `@nuxt/scripts` – Script optimization
