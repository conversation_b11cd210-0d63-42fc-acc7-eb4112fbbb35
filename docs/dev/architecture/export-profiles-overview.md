# Export Profiles Feature

## Purpose
Automated system for exporting specific data to external systems via FTP when orders are validated, with configurable templates.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/admin/export-profiles/index.vue` – Export profiles list
- `/pages/admin/export-profiles/[id].vue` – Profile configuration with wizard (stepper)

### Components
#### Step Wrapper
- `/components/export-profiles/ExportProfileStepWrapper.vue` – Wizard step wrapper
- `/components/export-profiles/ExportProfilesList.vue` – List of configured profiles

#### Forms
- `/components/export-profiles/forms/ExportProfileConfigurationForm.vue` – Basic configuration
- `/components/export-profiles/forms/ExportProfileDirectoriesForm.vue` – Directory selection
- `/components/export-profiles/forms/ExportProfileTemplateForm.vue` – Template configuration

### API Services
- `/api/export-profiles/` – CRUD and profile operations

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API operations
- Uses `@vueuse/core` for template editing functionality
