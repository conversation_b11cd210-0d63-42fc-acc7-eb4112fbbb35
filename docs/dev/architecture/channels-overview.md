# Channels Feature

## Purpose
External entry points that allow receiving orders from third-party systems (such as MYNUMA), with AI mapping to convert external formats.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/admin/channels/index.vue` – Channel list

### Components
#### Forms
- `/components/forms/channels/UpsertMynuma.vue` – Form for configuring the MYNUMA channel

### Stores
- `/stores/channels.store.ts` – State and APIs for channel CRUD operations

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for API operations
