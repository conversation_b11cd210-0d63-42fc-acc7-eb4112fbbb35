# Packages Overview

## Key Dependencies

The following packages are configured in `package.json`:

### Core Framework
- `nuxt: ^3.17.5` – Vue.js framework for modern web applications
- `pinia: ^3.0.3` – Reactive state management for Vue.js
- `@pinia/nuxt: ^0.10.1` – Automatic Pinia integration with Nuxt

### UI & Components
- `@nuxt/ui-pro: ^3.0.2` – Premium UI component system for Nuxt
- `@tanstack/vue-table: ^8.21.2` – Advanced tables with sorting, filtering, and pagination
- `@iconify-json/ph: ^1.2.2` – Phosphor icon pack

### Data Fetching & Cache
- `@tanstack/vue-query: ^5.81.2` – Data fetching and caching
- `@tanstack/vue-query-devtools: ^5.83.0` – Devtools for vue-query
- `@lukemorales/query-key-factory: ^1.3.4` – Unique query key generator

### Utilities
- `@vueuse/core: ^13.1.0` – Useful Vue composables (useClipboard, useWindowSize, etc.)
- `lodash: ^4.17.21` – JavaScript utility functions (cloneDeep, debounce, groupBy)
- `dayjs-nuxt: ^2.1.11` – Date/time formatting and manipulation
- `qs: ^6.14.0` – Converts TypeScript objects to query strings and vice versa

### Authentication & Security
- `nuxt-auth-sanctum: ^0.6.1` – Authentication with Laravel Sanctum
- `pkce-challenge: ^5.0.0` – PKCE implementation for secure OAuth flow

### Internationalization & Optimization
- `@nuxtjs/i18n: ^9.4.0` – Multi-language support (English/Dutch)
- `@nuxt/image: ^1.10.0` – Automatic image optimization and lazy loading
- `@nuxt/scripts: ^0.11.8` – Optimized loading of external scripts

### Development
- `@nuxt/eslint: ^1.3.0`
- `typescript: ^5.8.2`
- `@antfu/eslint-config: ^4.13.0` – ESLint configuration
- `husky: ^9.1.7` – Runs custom scripts (e.g. linters) before commits
- `lint-staged: ^15.5.2` – Runs linters only on staged files
