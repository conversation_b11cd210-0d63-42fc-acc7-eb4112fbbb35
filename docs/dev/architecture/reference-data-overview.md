# Reference Data Feature

## Purpose
Allows users to view, edit, import, and export reference information such as products, suppliers, categories, etc.

---------------------------------------------------------------|
## File Structure

### Pages
- `/pages/reference-data/index.vue` – Main dashboard for reference data
- `/pages/reference-data/imports/index.vue` – Interface for managing imports

### Components
#### Tables & Display
- `/components/tables/renderer/AssetTable.vue` – Renders reference data tables
- `/components/tables/renderer/types/Relationship.vue` – Handles relationship display

#### Import Components
- `/components/forms/imports/AddImport.vue` – Create new import process
- `/components/forms/imports/EditImport.vue` – Edit import configuration

### API Services
- `/api/imports/` – Import process management
- `/api/asset-types/` – Asset type configuration

### Stores
- `/stores/asset.store.ts` – Dynamic factory for table stores
- `/stores/asset-factory.store.ts` – Factory pattern management
- `/stores/import.store.ts` – Import process management

---------------------------------------------------------------|
## Global Dependencies
- Uses `vue-query.ts` for data operations
- Uses `@vueuse/core` for utilities
- Uses `lodash` via `nuxt-lodash` for data operations
