<script setup lang="ts">
import type { AssetTypeData } from '~/utils/types/api/api'
import {
  FormsAssetTypeAddAssetTypeFieldForm,
  FormsAssetTypeAddAssetTypeIndexForm,
  FormsAssetTypeAddAssetTypeRelationshipForm,
  FormsAssetTypeUpsertAssetTypeFormFieldForm,
  FormsAssetTypeUpsertAssetTypeTableFieldForm,
} from '#components'
import { useGenerateAssetTypeFormFieldsMutation } from '~/api/asset-type-form-fields/mutations/useAssetTypeFormFieldMutations'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const route = useRoute()
const overlay = useOverlay()
const toast = useToast()

const assetTypeStore = useAssetTypeStore()

const assetType = ref<AssetTypeData | undefined>()
const currentTab = ref<string>('fields')
const assetTypeId = route.params.id as string

// Modals
const fieldModal = overlay.create(FormsAssetTypeAddAssetTypeFieldForm)
const relationshipModal = overlay.create(FormsAssetTypeAddAssetTypeRelationshipForm)
const indexModal = overlay.create(FormsAssetTypeAddAssetTypeIndexForm)
const formFieldModal = overlay.create(FormsAssetTypeUpsertAssetTypeFormFieldForm)
const tableFieldModal = overlay.create(FormsAssetTypeUpsertAssetTypeTableFieldForm)

// Mutations
const generateAssetTypeFormFieldsMutation = useGenerateAssetTypeFormFieldsMutation()

onMounted(async () => {
  assetType.value = assetTypeStore.getById(assetTypeId)
})

const buttonLabel = computed<string>(() => {
  return `Add ${currentTab.value}`
})

const links = computed(() => {
  return [
    {
      label: 'Fields',
      active: currentTab.value === 'fields',
      onSelect: () => setTab('fields'),
    },
    {
      label: 'Relationships',
      active: currentTab.value === 'relationships',
      onSelect: () => setTab('relationships'),
    },
    {
      label: 'Indexes',
      active: currentTab.value === 'indexes',
      onSelect: () => setTab('indexes'),
    },
    {
      label: 'Form fields',
      active: currentTab.value === 'formFields',
      onSelect: () => setTab('formFields'),
    },
    {
      label: 'Table fields',
      active: currentTab.value === 'tableFields',
      onSelect: () => setTab('tableFields'),
    },
  ]
})

function buttonAction() {
  if (currentTab.value === 'fields') {
    fieldModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === 'relationships') {
    relationshipModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === 'indexes') {
    indexModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === 'formFields') {
    formFieldModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === 'tableFields') {
    tableFieldModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
}

async function generateAction() {
  try {
    await generateAssetTypeFormFieldsMutation.mutateAsync(assetTypeId)
    toast.add({
      title: 'Form fields generated successfully',
      color: 'success',
    })
  }
  catch (e) {
    toast.add({
      title: 'Error generating form fields',
      description: (e as any)?.data?.message || (e as any)?.message || 'An error occurred',
      color: 'error',
    })
  }
}

function setTab(tab: string) {
  currentTab.value = tab
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="`Master data: ${assetType?.label}`">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            v-if="currentTab === 'formFields'"
            label="generate"
            leading-icon="i-ph-arrow-clockwise"
            variant="outline"
            :loading="generateAssetTypeFormFieldsMutation.isPending.value"
            :disabled="generateAssetTypeFormFieldsMutation.isPending.value"
            @click="generateAction"
          />
          <UButton
            :label="buttonLabel"
            leading-icon="i-ph-plus"
            @click="buttonAction"
          />
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar>
        <UNavigationMenu :items="links" highlight class="-mx-1 flex-1" />
      </UDashboardToolbar>
    </template>
    <template #body>
      <div v-if="assetType">
        <div v-if="currentTab === 'fields'">
          <TablesAssetTypeFieldsTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === 'relationships'">
          <TablesAssetTypeRelationshipsTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === 'indexes'">
          <TablesAssetTypeIndexesTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === 'formFields'">
          <TablesAssetTypeFormFieldsTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === 'tableFields'">
          <TablesAssetTypeTableFieldsTable :asset-type-id="assetTypeId" />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
